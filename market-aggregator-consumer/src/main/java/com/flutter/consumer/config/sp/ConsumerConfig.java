package com.flutter.consumer.config.sp;

import com.betfair.platform.fms.MarketChangeListener;
import com.betfair.platform.fms.cache.MarketViewCache;
import com.betfair.platform.fms.model.MarketChange;
import com.betfair.platform.fms.model.MarketChanges;
import com.betfair.platform.fms.model.MarketView;
import com.betfair.platform.fms.notification.MarketStreamClientNotificationService;
import com.flutter.consumer.config.props.ConsumerProperties;
import com.flutter.consumer.factory.ConsumerContainerFactory;
import com.flutter.consumer.mappings.updates.MappingUpdate;
import com.flutter.consumer.mappings.view.MappingView;
import com.flutter.consumer.stream.MappingsConsumerDeltaApplier;
import com.flutter.consumer.stream.MappingsPayloadDeserializer;
import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.stream.consumer.ConsumerContainer;
import com.ppb.platform.stream.consumer.core.datasource.StreamConsumerDatasource;
import com.ppb.platform.stream.consumer.core.datasource.inmemory.InMemoryStreamConsumerDatasource;
import com.ppb.platform.stream.consumer.core.indexing.IndexFunction;
import com.ppb.platform.stream.consumer.core.notifier.LoggingStreamNotificationObserver;
import com.ppb.platform.stream.consumer.core.notifier.StreamNotificationObserver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import scala.Option;
import scala.collection.JavaConversions;
import scala.collection.immutable.List;

import javax.annotation.PostConstruct;
import java.util.Collections;

@Configuration
@ImportResource("classpath:conf/fms-new-client-application.xml")
public class ConsumerConfig {

//    @Bean
//    public StreamNotificationObserver<MarketView, MarketChange> streamNotificationObserver() {
//        return new LoggingStreamNotificationObserver<>();
//    }
//
//    @Bean
//    public StreamConsumerDatasource<MarketView> streamConsumerDatasource() {
//        return new InMemoryStreamConsumerDatasource<>();
//    }
//
//    @Bean
//    public ConsumerContainer<MarketView, MarketChanges, MarketChange> consumerContainer(
//            ConsumerProperties consumerProperties,
//            MappingsConsumerDeltaApplier consumerDeltaApplier,
//            MappingsPayloadDeserializer payloadDeserializer,
//            StreamNotificationObserver<MarketView, MarketChange> streamNotificationObserver,
//            StreamConsumerDatasource<MarketView> streamConsumerDatasource
//    ) {
//        List<StreamNotificationObserver<MarketView, MarketChange>> streamNotificationObserverList =
//                JavaConversions.asScalaBuffer(
//                        java.util.List.of(streamNotificationObserver)
//                ).toList();
//
//        return ConsumerContainerFactory.newInstance(
//                consumerProperties,
//                consumerDeltaApplier,
//                payloadDeserializer,
//                streamNotificationObserverList,
//                streamConsumerDatasource,
//                Option.empty(),
//                JavaConversions.<IndexFunction<Common.Mappings>>asScalaBuffer(Collections.emptyList()).toSet()
//        );
//    }

    @Bean(initMethod = "start")
    public MarketStreamClientWrapper marketStreamClientWrapper(MarketStreamClient marketStreamClient) {
        return new MarketStreamClientWrapper(marketStreamClient);
    }

    @Bean
    public CustomMarketChangeListener customMarketChangeListener(MarketStreamClientNotificationService marketStreamClientNotificationService) {
        CustomMarketChangeListener customMarketChangeListener = new CustomMarketChangeListener();
        marketStreamClientNotificationService.addMarketChangeListener(customMarketChangeListener);
        return customMarketChangeListener;
    }


}
